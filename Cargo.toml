[workspace]
members = [
    "programs/*",
]
resolver = "2"

[package]
name = "MEV_Bot_Solana"
version = "0.1.0"
edition = "2021"

[dependencies]
# lb_clmm = { path = "./programs/lb_clmm" }
raydium_amm = { path = "./programs/raydium_amm", features = ["cpi"] }
url = "2.3.1"
dotenv = "0.15.0"
anyhow = "1.0.70"
itertools = "0.11.0"
serde = "1.0.188"
serde_json = { version = "1.0.107", features = ["std"] }
bounded-vec-deque = "0.1.1"

# Telegram
teloxide = { version = "0.12", features = ["macros"] }

futures = "0.3.5"
futures-util = "*"
tokio = { version = "1.29.0", features = ["full"] }
tokio-stream = { version = "0.1", features = ['sync'] }
tokio-tungstenite = "*"
async-trait = "0.1.74"
eth-encode-packed = "0.1.0"
rlp = { version = "0.5", features = ["derive"] }

csv = "1.2.2"
colored = "2.0.0"
log = "0.4.21"
fern = { version = "0.6.2", features = ["colored"] }
chrono = "0.4.23"
indicatif = "0.17.5"
ndarray = "0.15.6"
web3 = "0.19.0"
eyre = "0.6.12"
reqwest = {version = "0.12.3", features = ["blocking","json", "stream"] }
strum = "0.26.2"
strum_macros = "0.26.2"
exitfailure = "0.5.1"
solana-sdk = "1.18.9"
solana-client = "1.18.10"
solana-program = "1.18.10"
thiserror = "1.0.58"
hex = "0.4.3"
solana-account-decoder = "1.18.12"
solana-pubsub-client = "1.18.12"
base64 = "0.22.1"
decimal = "2.1.0"
rust_decimal = {version = "1.35.0", features = ["maths"]}
num = "0.4.2"
borsh = "1.5.0"
rust_decimal_macros = "1.34.2"
log4rs = "1.3.0"
rust_socketio = {version = "*", features = ["async"]}
ws = "0.9.2"
anchor-client = {version = "0.30.0", features = ["async"]}
clap = "4.5.4"
anchor-lang = { version = "0.30.0", features = ["event-cpi"] }
anchor-spl = "0.30.0"
serum_dex = "0.5.4"
safe-transmute = "0.11.3"
spl-associated-token-account = "3.0.2"
anchor-safe-math = "0.5.0"
num-bigint = "0.4.5"
solana-transaction-status = "1.18.15"
plotters = { version = "^0.3.5", default-features = false, features = ["evcxr", "all_series", "all_elements"] }
piston_window = "0.132.0"
plotters-piston = "0.3.0"
systemstat = "0.2.3"
mongodb = {version = "2.8.2", default-features = false, features = ["async-std-runtime"] }

[profile.release]
codegen-units = 1
lto = "fat"

