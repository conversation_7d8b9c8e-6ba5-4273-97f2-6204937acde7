[package]
name = "raydium_amm"
version = "0.3.0"
description = "Minimal Raydium AMM program for swap functionality"
edition = "2021"

[lib]
crate-type = ["cdylib", "lib"]
name = "raydium_amm"

[features]
no-entrypoint = []
no-idl = []
no-log-ix-name = []
cpi = ["no-entrypoint"]
default = ["no-entrypoint"]

[dependencies]
anchor-lang = { version = "0.30.0", features = ["event-cpi"] }
anchor-spl = "0.30.0"
solana-program = "1.18.10"
spl-token = "4.0.0"
spl-associated-token-account = "3.0.2"
arrayref = "0.3.7"
safe-transmute = "0.11.3"
serde = { version = "1.0.188", features = ["derive"] }
serde_json = "1.0.107"
thiserror = "1.0.58"
