# Solana MEV Bot Setup Guide

This guide will help you configure and run the Solana MEV Bot.

## Prerequisites

1. **Rust**: Install from [rustup.rs](https://rustup.rs/)
2. **Solana CLI**: Install from [Solana's official documentation](https://docs.solana.com/cli/install-solana-cli-tools)
3. **MongoDB**: Install locally or use MongoDB Atlas
4. **Solana Wallet**: A funded wallet for trading

## Configuration Steps

### 1. Environment Variables

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

Edit `.env` with your actual values:

#### Required Configuration:

- **PAYER_KEYPAIR_PATH**: Path to your wallet keypair JSON file
- **RPC_URL**: Your Solana RPC endpoint
- **RPC_URL_TX**: Fast RPC endpoint for transaction sending
- **DATABASE_NAME**: MongoDB database name (default: MEV_Bot)

#### Optional but Recommended:

- **BLOCK_ENGINE_URL**: For MEV operations (default: Jito)
- **GEYSER_URL**: For real-time data streaming
- **SIMULATOR_URL**: For transaction simulation

### 2. Wallet Setup

Create a new wallet or use an existing one:

```bash
# Create new wallet
solana-keygen new --outfile ./wallet.json

# Or use existing wallet
cp /path/to/your/wallet.json ./wallet.json
```

Update your `.env` file:
```
PAYER_KEYPAIR_PATH=./wallet.json
```

### 3. RPC Provider Setup

For optimal performance, use a premium RPC provider:

#### Recommended Providers:

1. **Helius** (Best for MEV):
   ```
   RPC_URL=https://rpc.helius.xyz/?api-key=YOUR_API_KEY
   RPC_URL_TX=https://rpc.helius.xyz/?api-key=YOUR_API_KEY
   WSS_RPC_URL=wss://rpc.helius.xyz/?api-key=YOUR_API_KEY
   ```

2. **QuickNode**:
   ```
   RPC_URL=https://your-endpoint.solana-mainnet.quiknode.pro/YOUR_API_KEY/
   RPC_URL_TX=https://your-endpoint.solana-mainnet.quiknode.pro/YOUR_API_KEY/
   WSS_RPC_URL=wss://your-endpoint.solana-mainnet.quiknode.pro/YOUR_API_KEY/
   ```

3. **Triton**:
   ```
   RPC_URL=https://your-endpoint.rpcpool.com/YOUR_API_KEY
   RPC_URL_TX=https://your-endpoint.rpcpool.com/YOUR_API_KEY
   WSS_RPC_URL=wss://your-endpoint.rpcpool.com/YOUR_API_KEY
   ```

### 4. Database Setup

#### Local MongoDB:
```bash
# Install MongoDB
brew install mongodb/brew/mongodb-community  # macOS
# or follow MongoDB installation guide for your OS

# Start MongoDB
brew services start mongodb/brew/mongodb-community
```

#### MongoDB Atlas (Cloud):
1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a cluster
3. Update connection string in database.rs if needed

### 5. Build and Test

```bash
# Install dependencies
cargo build

# Test configuration
cargo run --release -- --test

# Run the bot
cargo run --release
```

## Configuration Examples

### Minimal Configuration (.env):
```
RPC_URL=https://api.mainnet-beta.solana.com
RPC_URL_TX=https://api.mainnet-beta.solana.com
DEVNET_RPC_URL=https://api.devnet.solana.com
WSS_RPC_URL=wss://api.mainnet-beta.solana.com
BLOCK_ENGINE_URL=https://mainnet.block-engine.jito.wtf
PAYER_KEYPAIR_PATH=./wallet.json
DATABASE_NAME=MEV_Bot
GEYSER_URL=
GEYSER_ACCESS_TOKEN=
SIMULATOR_URL=
WS_SIMULATOR_URL=
MAINNET_RPC_URL=https://api.mainnet-beta.solana.com
```

### Production Configuration (.env):
```
RPC_URL=https://rpc.helius.xyz/?api-key=YOUR_HELIUS_KEY
RPC_URL_TX=https://rpc.helius.xyz/?api-key=YOUR_HELIUS_KEY
DEVNET_RPC_URL=https://api.devnet.solana.com
WSS_RPC_URL=wss://rpc.helius.xyz/?api-key=YOUR_HELIUS_KEY
BLOCK_ENGINE_URL=https://mainnet.block-engine.jito.wtf
PAYER_KEYPAIR_PATH=./wallet.json
DATABASE_NAME=MEV_Bot_Production
GEYSER_URL=grpc://your-endpoint.rpcpool.com:443
GEYSER_ACCESS_TOKEN=YOUR_GEYSER_TOKEN
SIMULATOR_URL=https://your-simulator-endpoint.com
WS_SIMULATOR_URL=wss://your-simulator-endpoint.com
MAINNET_RPC_URL=https://rpc.helius.xyz/?api-key=YOUR_HELIUS_KEY
```

## Troubleshooting

### Common Issues:

1. **Wallet not found**: Ensure PAYER_KEYPAIR_PATH points to a valid JSON keypair file
2. **RPC errors**: Check your RPC provider limits and API keys
3. **Database connection**: Ensure MongoDB is running and accessible
4. **Insufficient funds**: Ensure your wallet has enough SOL for transactions

### Logs:
- Program logs: `logs/program.log`
- Error logs: `logs/errors.log`

## Security Notes

- Never commit your `.env` file to version control
- Keep your wallet keypair secure
- Use environment-specific configurations
- Monitor your wallet balance and transaction costs

## Next Steps

After configuration:
1. Test with small amounts first
2. Monitor logs for any issues
3. Adjust strategy parameters as needed
4. Set up monitoring and alerts
